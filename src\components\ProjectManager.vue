<script setup lang="ts">
import { ref, onMounted } from 'vue'
import notification from '../utils/notification'
import projectApi, { type Project, type LogEntry, type CreateProjectRequest } from '../api/index'

const projects = ref<Project[]>([])
const showAddForm = ref(false)
const showLogs = ref(false)
const selectedProject = ref<string>('')
const logs = ref<LogEntry[]>([])
const loading = ref(false)
const showConfirmDialog = ref(false)
const confirmProjectName = ref('')

// Mock 数据
const mockProjects: Project[] = [
  {
    id: 123,
    name: 'frontend-app',
    http_url_to_repo: 'https://gitlab.example.com/team/frontend-app.git',
    status: 'active',
    lastBackup: '2024-01-15 10:30:00'
  },
  {
    id: 124,
    name: 'backend-api',
    http_url_to_repo: 'https://gitlab.example.com/team/backend-api.git',
    status: 'active',
    lastBackup: '2024-01-15 09:15:00'
  },
  {
    id: 125,
    name: 'mobile-app',
    http_url_to_repo: 'https://gitlab.example.com/team/mobile-app.git',
    status: 'inactive',
    lastBackup: '2024-01-14 16:45:00'
  },
  {
    id: 126,
    name: 'data-service',
    http_url_to_repo: 'https://gitlab.example.com/team/data-service.git',
    status: 'active',
    lastBackup: '2024-01-15 08:20:00'
  },
  {
    id: 127,
    name: 'auth-service',
    http_url_to_repo: 'https://gitlab.example.com/team/auth-service.git',
    status: 'active',
    lastBackup: '2024-01-15 11:45:00'
  },
  {
    id: 123,
    name: 'frontend-app',
    http_url_to_repo: 'https://gitlab.example.com/team/frontend-app.git',
    status: 'active',
    lastBackup: '2024-01-15 10:30:00'
  }
]

const mockLogs: Record<string, LogEntry[]> = {
  'frontend-app': [
    { timestamp: '2024-01-15 10:30:00', message: '备份完成', level: 'success' },
    { timestamp: '2024-01-15 10:29:45', message: '开始同步代码', level: 'info' },
    { timestamp: '2024-01-15 10:29:30', message: '连接到远程仓库', level: 'info' }
  ],
  'backend-api': [
    { timestamp: '2024-01-15 09:15:00', message: '备份完成', level: 'success' },
    { timestamp: '2024-01-15 09:14:30', message: '检测到新提交', level: 'info' }
  ],
  'mobile-app': [
    { timestamp: '2024-01-14 16:45:00', message: '备份失败：网络超时', level: 'error' },
    { timestamp: '2024-01-14 16:44:30', message: '开始备份', level: 'info' }
  ],
  'data-service': [
    { timestamp: '2024-01-15 08:20:00', message: '备份完成', level: 'success' },
    { timestamp: '2024-01-15 08:19:30', message: '开始同步代码', level: 'info' }
  ],
  'auth-service': [
    { timestamp: '2024-01-15 11:45:00', message: '备份完成', level: 'success' },
    { timestamp: '2024-01-15 11:44:15', message: '检测到新提交', level: 'info' }
  ]
}

// 新项目表单数据
const newProject = ref({
  id: '',
  name: '',
  http_url_to_repo: ''
})

onMounted(async () => {
  try {
    // 使用统一的 API 调用获取项目列表
    projects.value = await projectApi.getProjects()
  } catch (error) {
    console.error('获取项目列表失败:', error)
    // 如果API调用失败，使用 mock 数据作为后备
    projects.value = [...mockProjects]
    notification.warning('无法连接到服务器，显示模拟数据', '连接失败')
  }
})

const addProject = async () => {
  if (!newProject.value.name || !newProject.value.http_url_to_repo) {
    notification.warning('请填写完整信息', '表单验证')
    return
  }

  loading.value = true
  try {
    // 使用统一的 API 调用添加项目
    const projectName = newProject.value.name
    const createdProject = await projectApi.createProject(newProject.value)

    // 添加到本地列表
    projects.value.push(createdProject)

    // 重置表单
    newProject.value = { id: '', name: '', http_url_to_repo: '' }
    showAddForm.value = false

    notification.success(`项目 ${projectName} 添加成功`, '操作成功')
  } catch (error) {
    console.error('添加项目失败:', error)

    // API 调用失败时的后备处理（使用 mock 数据）
    const id = Date.now()
    const projectName = newProject.value.name
    projects.value.push({
      id,
      name: newProject.value.name,
      http_url_to_repo: newProject.value.http_url_to_repo,
      status: 'active',
      lastBackup: '未备份'
    })

    // 重置表单
    newProject.value = { id: '', name: '', http_url_to_repo: '' }
    showAddForm.value = false

    notification.warning(`项目 ${projectName} 添加成功（模拟数据）`, '操作成功')
  } finally {
    loading.value = false
  }
}

const triggerBackup = async (projectName: string) => {
  loading.value = true
  try {
    // 使用统一的 API 调用触发备份
    await projectApi.triggerBackup(projectName)

    // 更新项目状态
    const project = projects.value.find(p => p.name === projectName)
    if (project) {
      project.lastBackup = new Date().toLocaleString()
    }

    notification.success(`项目 ${projectName} 备份已触发`, '备份操作')
  } catch (error) {
    console.error('触发备份失败:', error)

    // API 调用失败时的后备处理（使用 mock 行为）
    await new Promise(resolve => setTimeout(resolve, 1000))

    const project = projects.value.find(p => p.name === projectName)
    if (project) {
      project.lastBackup = new Date().toLocaleString()
    }

    notification.warning(`项目 ${projectName} 备份已触发（模拟数据）`, '备份操作')
  } finally {
    loading.value = false
  }
}

const viewLogs = async (projectName: string) => {
  selectedProject.value = projectName
  loading.value = true
  try {
    // 使用统一的 API 调用获取日志
    logs.value = await projectApi.getProjectLogs(projectName)
    showLogs.value = true
  } catch (error) {
    console.error('获取日志失败:', error)

    // API 调用失败时的后备处理（使用 mock 数据）
    await new Promise(resolve => setTimeout(resolve, 300))
    logs.value = mockLogs[projectName] || []
    showLogs.value = true

    notification.warning('无法连接到服务器，显示模拟日志', '连接失败')
  } finally {
    loading.value = false
  }
}

const deleteProject = (projectName: string) => {
  confirmProjectName.value = projectName
  showConfirmDialog.value = true
}

const confirmDelete = async () => {
  const projectName = confirmProjectName.value
  showConfirmDialog.value = false

  loading.value = true
  try {
    // 使用统一的 API 调用删除项目
    await projectApi.deleteProject(projectName)

    // 从本地列表中移除
    projects.value = projects.value.filter(p => p.name !== projectName)

    notification.success(`项目 ${projectName} 已删除`, '删除成功')
  } catch (error) {
    console.error('删除项目失败:', error)

    // API 调用失败时的后备处理（使用 mock 行为）
    await new Promise(resolve => setTimeout(resolve, 500))
    projects.value = projects.value.filter(p => p.name !== projectName)

    notification.warning(`项目 ${projectName} 已删除（模拟数据）`, '删除成功')
  } finally {
    loading.value = false
  }
}

const cancelDelete = () => {
  showConfirmDialog.value = false
  confirmProjectName.value = ''
}

const closeLogs = () => {
  showLogs.value = false
  selectedProject.value = ''
  logs.value = []
}
</script>

<template>
  <div class="project-manager">
    <!-- 头部 -->
    <div class="header">
      <h1>代码同步管理</h1>
      <button @click="showAddForm = true" class="btn btn-primary">
        <span class="btn-icon">+</span>
        添加项目
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading">
        <div class="spinner"></div>
        <span>处理中...</span>
      </div>
    </div>

    <!-- 项目表格 -->
    <div class="table-container">
      <div class="table-wrapper">
        <table class="project-table">
          <thead>
            <tr>
              <th>项目名称</th>
              <th>仓库地址</th>
              <th>状态</th>
              <th>最后备份时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="projects.length === 0">
              <td colspan="5" class="empty-state">暂无项目，点击"添加项目"开始管理您的代码仓库</td>
            </tr>
            <tr v-for="project in projects" :key="project.id" class="project-row">
              <td class="project-name">{{ project.name }}</td>
              <td class="repo-url">{{ project.http_url_to_repo }}</td>
              <td>
                <span class="status-badge" :class="project.status">
                  {{ project.status === 'active' ? '活跃' : '非活跃' }}
                </span>
              </td>
              <td class="backup-time">{{ project.lastBackup }}</td>
              <td class="actions">
                <button
                  @click="triggerBackup(project.name)"
                  class="btn btn-sm btn-success"
                  :disabled="loading"
                  title="触发备份"
                >
                  备份
                </button>
                <button
                  @click="viewLogs(project.name)"
                  class="btn btn-sm btn-info"
                  :disabled="loading"
                  title="查看日志"
                >
                  日志
                </button>
                <button
                  @click="deleteProject(project.name)"
                  class="btn btn-sm btn-danger"
                  :disabled="loading"
                  title="删除项目"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 添加项目弹窗 -->
    <Transition name="modal" appear>
      <div v-if="showAddForm" class="modal-overlay" @click="showAddForm = false">
        <div class="modal" @click.stop>
          <div class="modal-header">
            <h2>添加新项目</h2>
            <button @click="showAddForm = false" class="close-btn">&times;</button>
          </div>
          <form @submit.prevent="addProject" class="modal-body">
            <div class="form-group">
              <label>项目名称:</label>
              <input
                v-model="newProject.name"
                type="text"
                required
                placeholder="例: frontend-app"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>仓库地址:</label>
              <input
                v-model="newProject.http_url_to_repo"
                type="url"
                required
                placeholder="https://gitlab.example.com/group/project.git"
                class="form-input"
              />
            </div>
            <div class="modal-actions">
              <button type="button" @click="showAddForm = false" class="btn btn-secondary">
                取消
              </button>
              <button type="submit" class="btn btn-primary" :disabled="loading">添加</button>
            </div>
          </form>
        </div>
      </div>
    </Transition>

    <!-- 日志弹窗 -->
    <Transition name="modal" appear>
      <div v-if="showLogs" class="modal-overlay" @click="closeLogs">
        <div class="modal modal-large" @click.stop>
          <div class="modal-header">
            <h2>{{ selectedProject }} - 同步日志</h2>
            <button @click="closeLogs" class="close-btn">&times;</button>
          </div>
          <div class="modal-body">
            <div v-if="logs.length === 0" class="empty-logs">
              <p>暂无日志记录</p>
            </div>
            <div v-else class="logs-container">
              <div v-for="log in logs" :key="log.timestamp" class="log-entry" :class="log.level">
                <span class="log-time">{{ log.timestamp }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 确认删除对话框 -->
    <Transition name="modal" appear>
      <div v-if="showConfirmDialog" class="modal-overlay" @click="cancelDelete">
        <div class="modal" @click.stop>
          <div class="modal-header">
            <h2>确认删除</h2>
            <button @click="cancelDelete" class="close-btn">&times;</button>
          </div>
          <div class="modal-body">
            <div class="confirm-content">
              <div class="confirm-icon">⚠️</div>
              <div class="confirm-text">
                <p>
                  确定要删除项目 <strong>{{ confirmProjectName }}</strong> 吗？
                </p>
                <p class="confirm-warning">此操作不可撤销</p>
              </div>
            </div>
            <div class="modal-actions">
              <button @click="cancelDelete" class="btn btn-secondary">取消</button>
              <button @click="confirmDelete" class="btn btn-danger" :disabled="loading">
                确认删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.project-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  color: #666;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.table-container {
  flex: 1;
  padding: 20px 30px;
  height: 0; /* 让flex容器正确计算高度 */
}

.table-wrapper {
  height: 100%;
  overflow: auto;
  border-radius: 8px;
}

.project-table {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-collapse: separate;
  border-spacing: 0;
}

.project-table th {
  background: #f8f9fa;
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e5e9;
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-table th:first-child {
  border-top-left-radius: 8px;
}

.project-table th:last-child {
  border-top-right-radius: 8px;
}

.project-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  vertical-align: middle;
}

.project-row:hover {
  background: #f8f9fa;
}

.project-name {
  font-weight: 600;
  color: #2c3e50;
}

.repo-url {
  color: #666;
  font-size: 13px;
  max-width: 300px;
  word-break: break-all;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.backup-time {
  color: #666;
  font-size: 13px;
}

.actions {
  white-space: nowrap;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-style: italic;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
  margin-right: 6px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #229954;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-icon {
  font-size: 16px;
  font-weight: bold;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.empty-logs {
  text-align: center;
  padding: 40px;
  color: #666;
}

.logs-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-entry {
  display: flex;
  gap: 15px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  border-left: 4px solid transparent;
}

.log-entry.info {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.log-entry.success {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.log-entry.error {
  background: #ffebee;
  border-left-color: #f44336;
}

.log-time {
  font-weight: 500;
  color: #666;
  min-width: 140px;
  font-family: 'Courier New', monospace;
}

.log-message {
  flex: 1;
}

/* 确认对话框内容样式 */
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
}

.confirm-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.confirm-text {
  flex: 1;
}

.confirm-text p {
  margin: 0 0 8px 0;
  color: #303133;
  line-height: 1.5;
  font-size: 14px;
}

.confirm-text p:last-child {
  margin-bottom: 0;
}

.confirm-text strong {
  color: #e74c3c;
  font-weight: 600;
}

.confirm-warning {
  font-size: 13px;
  color: #909399;
}

/* 模态框过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-active .modal-overlay,
.modal-leave-active .modal-overlay {
  transition: opacity 0.3s ease;
}

.modal-enter-active .modal,
.modal-leave-active .modal {
  transition: all 0.3s ease;
}

.modal-enter-from .modal-overlay,
.modal-leave-to .modal-overlay {
  opacity: 0;
}

.modal-enter-from .modal,
.modal-leave-to .modal {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.modal-enter-to .modal-overlay,
.modal-leave-from .modal-overlay {
  opacity: 1;
}

.modal-enter-to .modal,
.modal-leave-from .modal {
  opacity: 1;
  transform: scale(1) translateY(0);
}

@media (max-width: 768px) {
  .header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .table-container {
    padding: 15px 20px;
  }

  .project-table {
    font-size: 13px;
  }

  .project-table th,
  .project-table td {
    padding: 12px 15px;
  }

  .repo-url {
    max-width: 200px;
  }

  .actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .btn-sm {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .modal {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .modal-body {
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .project-table th:nth-child(2),
  .project-table td:nth-child(2) {
    display: none;
  }
}
</style>
